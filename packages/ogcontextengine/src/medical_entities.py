"""
Medical Entities - Data models for medical information

TODO: Extend these with your domain-specific medical entity models.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum


class SeverityLevel(Enum):
    """Severity levels for symptoms."""
    MILD = 1
    MODERATE = 2
    SEVERE = 3
    CRITICAL = 4


class EntityType(Enum):
    """Types of medical entities."""
    PATIENT = "patient"
    SYMPTOM = "symptom"
    MEDICATION = "medication"
    DIAGNOSIS = "diagnosis"
    TREATMENT = "treatment"
    VITAL_SIGN = "vital_sign"


@dataclass
class MedicalEntity:
    """Base class for all medical entities."""
    id: str
    entity_type: EntityType
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entity to dictionary representation."""
        return {
            'id': self.id,
            'entity_type': self.entity_type.value,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }


@dataclass
class Patient(MedicalEntity):
    """Patient entity."""
    name: str
    age: Optional[int] = None
    gender: Optional[str] = None
    medical_history: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        self.entity_type = EntityType.PATIENT
        
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'age': self.age,
            'gender': self.gender,
            'medical_history': self.medical_history
        })
        return base_dict


@dataclass
class Symptom(MedicalEntity):
    """Symptom entity."""
    name: str
    severity: int  # 1-10 scale
    description: Optional[str] = None
    location: Optional[str] = None
    duration: Optional[str] = None
    triggers: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        self.entity_type = EntityType.SYMPTOM
        if not 1 <= self.severity <= 10:
            raise ValueError("Severity must be between 1 and 10")
            
    def get_severity_level(self) -> SeverityLevel:
        """Get the severity level enum."""
        if self.severity <= 3:
            return SeverityLevel.MILD
        elif self.severity <= 6:
            return SeverityLevel.MODERATE
        elif self.severity <= 8:
            return SeverityLevel.SEVERE
        else:
            return SeverityLevel.CRITICAL
            
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'severity': self.severity,
            'severity_level': self.get_severity_level().name,
            'description': self.description,
            'location': self.location,
            'duration': self.duration,
            'triggers': self.triggers
        })
        return base_dict


@dataclass
class Medication(MedicalEntity):
    """Medication entity."""
    name: str
    dosage: str
    frequency: str
    route: Optional[str] = None  # oral, injection, etc.
    indication: Optional[str] = None  # what it's for
    side_effects: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        self.entity_type = EntityType.MEDICATION
        
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'name': self.name,
            'dosage': self.dosage,
            'frequency': self.frequency,
            'route': self.route,
            'indication': self.indication,
            'side_effects': self.side_effects
        })
        return base_dict


@dataclass
class VitalSign(MedicalEntity):
    """Vital sign measurement."""
    measurement_type: str  # temperature, blood_pressure, heart_rate, etc.
    value: float
    unit: str
    normal_range: Optional[Dict[str, float]] = None
    
    def __post_init__(self):
        self.entity_type = EntityType.VITAL_SIGN
        
    def is_abnormal(self) -> bool:
        """Check if the vital sign is outside normal range."""
        if not self.normal_range:
            return False
            
        min_val = self.normal_range.get('min')
        max_val = self.normal_range.get('max')
        
        if min_val is not None and self.value < min_val:
            return True
        if max_val is not None and self.value > max_val:
            return True
            
        return False
        
    def to_dict(self) -> Dict[str, Any]:
        base_dict = super().to_dict()
        base_dict.update({
            'measurement_type': self.measurement_type,
            'value': self.value,
            'unit': self.unit,
            'normal_range': self.normal_range,
            'is_abnormal': self.is_abnormal()
        })
        return base_dict


def create_entity_from_dict(data: Dict[str, Any]) -> MedicalEntity:
    """Factory function to create medical entities from dictionary data."""
    entity_type = EntityType(data['entity_type'])
    timestamp = datetime.fromisoformat(data['timestamp'])
    
    if entity_type == EntityType.PATIENT:
        return Patient(
            id=data['id'],
            timestamp=timestamp,
            name=data['name'],
            age=data.get('age'),
            gender=data.get('gender'),
            medical_history=data.get('medical_history', []),
            metadata=data.get('metadata', {})
        )
    elif entity_type == EntityType.SYMPTOM:
        return Symptom(
            id=data['id'],
            timestamp=timestamp,
            name=data['name'],
            severity=data['severity'],
            description=data.get('description'),
            location=data.get('location'),
            duration=data.get('duration'),
            triggers=data.get('triggers', []),
            metadata=data.get('metadata', {})
        )
    elif entity_type == EntityType.MEDICATION:
        return Medication(
            id=data['id'],
            timestamp=timestamp,
            name=data['name'],
            dosage=data['dosage'],
            frequency=data['frequency'],
            route=data.get('route'),
            indication=data.get('indication'),
            side_effects=data.get('side_effects', []),
            metadata=data.get('metadata', {})
        )
    elif entity_type == EntityType.VITAL_SIGN:
        return VitalSign(
            id=data['id'],
            timestamp=timestamp,
            measurement_type=data['measurement_type'],
            value=data['value'],
            unit=data['unit'],
            normal_range=data.get('normal_range'),
            metadata=data.get('metadata', {})
        )
    else:
        # Generic entity for unknown types
        return MedicalEntity(
            id=data['id'],
            entity_type=entity_type,
            timestamp=timestamp,
            metadata=data.get('metadata', {})
        )

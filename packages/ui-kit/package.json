{"name": "ui-kit", "version": "0.1.0", "description": "SymptomOS UI Kit - Shared React components and design system", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "keywords": ["react", "components", "ui", "design-system", "symptomos"], "author": "SymptomOS Team", "license": "MIT", "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dependencies": {"clsx": "^2.1.0", "lucide-react": "^0.263.0", "date-fns": "^3.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "jest": "^29.7.0", "rollup": "^4.9.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.17", "postcss": "^8.4.33", "typescript": "^5.3.0"}}
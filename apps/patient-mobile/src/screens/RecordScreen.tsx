import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';

const { width } = Dimensions.get('window');

interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  uri: string | null;
}

const RecordScreen: React.FC = () => {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    uri: null,
  });
  const [permissionResponse, requestPermission] = Audio.usePermissions();
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    if (recordingState.isRecording) {
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [recordingState.isRecording]);

  const startRecording = async () => {
    try {
      if (permissionResponse?.status !== 'granted') {
        console.log('Requesting permission..');
        await requestPermission();
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      console.log('Starting recording..');
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);
      setRecordingState(prev => ({ ...prev, isRecording: true }));
      console.log('Recording started');
    } catch (err) {
      console.error('Failed to start recording', err);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    console.log('Stopping recording..');
    if (!recording) return;

    setRecordingState(prev => ({ ...prev, isRecording: false }));
    await recording.stopAndUnloadAsync();
    await Audio.setAudioModeAsync({
      allowsRecordingIOS: false,
    });
    const uri = recording.getURI();
    setRecordingState(prev => ({ ...prev, uri }));
    setRecording(null);
    console.log('Recording stopped and stored at', uri);

    // TODO: Send to parser service
    Alert.alert(
      'Recording Complete',
      'Your symptom has been recorded. Processing with IBM Granite...',
      [
        { text: 'OK', onPress: () => processRecording(uri) }
      ]
    );
  };

  const processRecording = async (uri: string | null) => {
    if (!uri) return;
    
    // TODO: Implement actual processing
    console.log('Processing recording:', uri);
    Alert.alert(
      'Processing Complete',
      'Your symptom has been analyzed and added to your timeline.',
      [
        { text: 'View Timeline', onPress: () => console.log('Navigate to timeline') },
        { text: 'Record Another', onPress: resetRecording },
      ]
    );
  };

  const resetRecording = () => {
    setRecordingState({
      isRecording: false,
      isPaused: false,
      duration: 0,
      uri: null,
    });
  };

  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Record Symptom</Text>
          <Text style={styles.subtitle}>
            Describe your symptoms naturally. I'll help you track them.
          </Text>
        </View>

        {/* Recording Visualization */}
        <View style={styles.recordingArea}>
          <Animated.View
            style={[
              styles.recordButton,
              {
                transform: [{ scale: pulseAnim }],
                backgroundColor: recordingState.isRecording ? '#ef4444' : '#3b82f6',
              },
            ]}
          >
            <TouchableOpacity
              style={styles.recordButtonInner}
              onPress={recordingState.isRecording ? stopRecording : startRecording}
              disabled={!permissionResponse?.granted}
            >
              <Ionicons
                name={recordingState.isRecording ? 'stop' : 'mic'}
                size={48}
                color="white"
              />
            </TouchableOpacity>
          </Animated.View>

          {recordingState.isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={styles.recordingText}>Recording...</Text>
            </View>
          )}

          <Text style={styles.duration}>
            {formatDuration(recordingState.duration)}
          </Text>
        </View>

        {/* Instructions */}
        <View style={styles.instructions}>
          <View style={styles.instructionItem}>
            <Ionicons name="mic-outline" size={20} color="#3b82f6" />
            <Text style={styles.instructionText}>
              Tap the microphone to start recording
            </Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="chatbubble-outline" size={20} color="#3b82f6" />
            <Text style={styles.instructionText}>
              Speak naturally about your symptoms
            </Text>
          </View>
          <View style={styles.instructionItem}>
            <Ionicons name="analytics-outline" size={20} color="#3b82f6" />
            <Text style={styles.instructionText}>
              AI will extract medical information
            </Text>
          </View>
        </View>

        {/* Quick Examples */}
        <View style={styles.examples}>
          <Text style={styles.examplesTitle}>Example phrases:</Text>
          <Text style={styles.exampleText}>
            "I have a headache, intensity 7 out of 10"
          </Text>
          <Text style={styles.exampleText}>
            "Took 500mg ibuprofen at 2 PM"
          </Text>
          <Text style={styles.exampleText}>
            "Feeling nauseous after eating lunch"
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  header: {
    alignItems: 'center',
    paddingTop: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
  },
  recordingArea: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  recordButton: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  recordButtonInner: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 24,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ef4444',
    marginRight: 8,
  },
  recordingText: {
    fontSize: 16,
    color: '#ef4444',
    fontWeight: '500',
  },
  duration: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1e293b',
    marginTop: 16,
    fontFamily: 'monospace',
  },
  instructions: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: '#64748b',
    marginLeft: 12,
    flex: 1,
  },
  examples: {
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  examplesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#475569',
    marginBottom: 8,
  },
  exampleText: {
    fontSize: 13,
    color: '#64748b',
    fontStyle: 'italic',
    marginBottom: 4,
  },
});

export default RecordScreen;
